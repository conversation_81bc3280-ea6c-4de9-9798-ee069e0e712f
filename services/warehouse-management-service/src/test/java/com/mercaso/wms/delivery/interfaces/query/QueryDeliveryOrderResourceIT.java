package com.mercaso.wms.delivery.interfaces.query;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderResourceApi;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryDeliveryOrderResourceIT extends AbstractIT {

    @Autowired
    private DeliveryOrderResourceApi deliveryOrderResourceApi;
    @Autowired
    private DeliveryOrderWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void whenFindById_thenReturnsDeliveryOrder() throws Exception {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        DeliveryOrderDto deliveryOrder = deliveryOrderResourceApi.findById(deliveryOrderDo.getId());

        // then
        assertNotNull(deliveryOrder);
        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertTrue(deliveryOrder.getShopifyOrderUrl().contains(shopifyOrderDto.getId()));
        assertEquals(shopifyOrderDto.getLineItems().size(), deliveryOrder.getDeliveryOrderItems().size());
    }

    @Test
    void whenFindByOrderNumber_thenReturnsDeliveryOrder() throws Exception {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        DeliveryOrderDto deliveryOrder = deliveryOrderResourceApi.findByOrderNumber(deliveryOrderDo.getOrderNumber());

        // then
        assertNotNull(deliveryOrder);
        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertTrue(deliveryOrder.getShopifyOrderUrl().contains(shopifyOrderDto.getId()));
        assertEquals(shopifyOrderDto.getLineItems().size(), deliveryOrder.getDeliveryOrderItems().size());
    }
}