package com.mercaso.wms.delivery.application.service;

import com.google.maps.model.LatLng;
import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShippingAddressDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderArrivedPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderInTransitPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderUnloadedPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderUpdatedPayloadDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryOrderUpdatedEventDto;
import com.mercaso.wms.delivery.application.mapper.deliveryorder.DeliveryOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.queryservice.DocumentQueryService;
import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.customer.CustomerService;
import com.mercaso.wms.delivery.domain.deliveryorder.Address;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import com.mercaso.wms.delivery.domain.route.RmRouteRepository;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.external.googlemap.GoogleMapAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.OnesignalAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.EEDataStatusUpdate;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.EEDataTimeInOrOut;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ExecutionEvent;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.ExecutionEventType;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.OrderStepType;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Application service for delivery order operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryOrderApplicationService {

    private final ImsAdaptor imsAdaptor;
    private final PgAdvisoryLock pgAdvisoryLock;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final WarehouseRepository warehouseRepository;
    private final DeliveryOrderDtoApplicationMapper mapper;
    private final BusinessEventDispatcher businessEventDispatcher;
    private final CustomerService customerService;
    private final DeliveryTaskService deliveryTaskService;
    private final RouteManagerAdaptor routeManagerAdaptor;
    private final RmRouteRepository rmRouteRepository;
    private final GoogleMapAdaptor googleMapAdaptor;
    private final ApplicationEventDispatcher applicationEventDispatcher;
    private final SlackDeliveryNotificationService slackNotificationService;
    private final OnesignalAdaptor onesignalAdaptor;
    private final DocumentApplicationService documentApplicationService;
    private final DocumentQueryService documentQueryService;

    public static final String MERCASO_SELLER = "SELLER_Mercaso";

    @RetryableTransaction
    public DeliveryOrderDto inTransit(UUID deliveryOrderId) {
        return updateStatus(deliveryOrderId, DeliveryOrderStatus.IN_TRANSIT);
    }

    @RetryableTransaction
    public DeliveryOrderDto delivery(UUID deliveryOrderId, DeliveryOrderDeliveredCommand command) {
        DeliveryOrderDto deliveryOrderDto = updateStatus(deliveryOrderId, DeliveryOrderStatus.DELIVERED, command);
        sendOrderDeliveredRmEvent(deliveryOrderDto);

        slackNotificationService.sendDeliveryCompletedNotification(deliveryOrderDto);

        return deliveryOrderDto;
    }

    private void sendOrderDeliveredRmEvent(DeliveryOrderDto deliveryOrderDto) {

        if (deliveryOrderDto == null || deliveryOrderDto.getDeliveryTaskId() == null) {
            log.warn("Unable to send delivery event: Order data is incomplete.");
            return;
        }

        List<RmRoute> rmRoutes = rmRouteRepository.findByDeliveryTaskId(deliveryOrderDto.getDeliveryTaskId());
        if (CollectionUtils.isEmpty(rmRoutes)) {
            log.warn("Unable to send delivery event: Route corresponding to order {} not found.", deliveryOrderDto.getId());
            return;
        }

        RmRoute rmRoute = rmRoutes.getFirst();
        ExecutionEvent timeOutEvent = buildTimeOutEvent(deliveryOrderDto, rmRoute);
        ExecutionEvent orderStatusUpdateEvent = buildOrderStatusUpdate(deliveryOrderDto, rmRoute);
        routeManagerAdaptor.sendExecutionEvents(List.of(timeOutEvent, orderStatusUpdateEvent));
    }

    private ExecutionEvent buildOrderStatusUpdate(DeliveryOrderDto deliveryOrderDto, RmRoute rmRoute) {
        return ExecutionEvent.builder()
            .type(ExecutionEventType.STATUS_UPDATE.value())
            .orderId(deliveryOrderDto.getRmOrderId())
            .vehicleId(rmRoute.getVehicleId())
            .orderStepType(OrderStepType.DELIVERY.getValue())
            .date(deliveryOrderDto.getDeliveryDate().replace("-", ""))
            .data(EEDataStatusUpdate.builder()
                .sec(DateUtils.secondsSinceMidnightTo(deliveryOrderDto.getDeliveredAt()))
                .status("done")
                .build())
            .build();
    }

    private ExecutionEvent buildTimeOutEvent(DeliveryOrderDto deliveryOrderDto, RmRoute rmRoute) {

        Instant deliveredAt = deliveryOrderDto.getDeliveredAt() == null ? Instant.now() : deliveryOrderDto.getDeliveredAt();

        return ExecutionEvent.builder()
            .type(ExecutionEventType.TIME_OUT.value())
            .orderId(deliveryOrderDto.getRmOrderId())
            .vehicleId(rmRoute.getVehicleId())
            .orderStepType(OrderStepType.DELIVERY.getValue())
            .date(deliveryOrderDto.getDeliveryDate().replace("-", ""))
            .data(EEDataTimeInOrOut.builder()
                .sec(DateUtils.secondsSinceMidnightTo(deliveredAt))
                .build())
            .build();
    }

    @RetryableTransaction
    public DeliveryOrderDto arrive(UUID deliveryOrderId) {
        DeliveryOrderDto deliveryOrderDto = updateStatus(deliveryOrderId, DeliveryOrderStatus.ARRIVED);
        sendTimeInExecutionEvent(deliveryOrderDto);
        return deliveryOrderDto;
    }

    private void sendTimeInExecutionEvent(DeliveryOrderDto deliveryOrderDto) {
        if (deliveryOrderDto == null || deliveryOrderDto.getDeliveryTaskId() == null) {
            log.warn("Unable to send arrival event: Order data is incomplete.");
            return;
        }

        List<RmRoute> rmRoutes = rmRouteRepository.findByDeliveryTaskId(deliveryOrderDto.getDeliveryTaskId());
        if (CollectionUtils.isEmpty(rmRoutes)) {
            log.warn("Unable to send arrival event: Route corresponding to order {} not found.", deliveryOrderDto.getId());
            return;
        }

        RmRoute rmRoute = rmRoutes.getFirst();
        ExecutionEvent event = buildTimeInEvent(deliveryOrderDto, rmRoute);
        routeManagerAdaptor.sendExecutionEvents(List.of(event));
    }

    private ExecutionEvent buildTimeInEvent(DeliveryOrderDto deliveryOrderDto, RmRoute rmRoute) {

        Instant arrivedAt = deliveryOrderDto.getArrivedAt() == null ? Instant.now() : deliveryOrderDto.getArrivedAt();

        return ExecutionEvent.builder()
            .type(ExecutionEventType.TIME_IN.value())
            .orderId(deliveryOrderDto.getRmOrderId())
            .vehicleId(rmRoute.getVehicleId())
            .orderStepType(OrderStepType.DELIVERY.getValue())
            .date(deliveryOrderDto.getDeliveryDate().replace("-", ""))
            .data(EEDataTimeInOrOut.builder()
                .sec(DateUtils.secondsSinceMidnightTo(arrivedAt))
                .build())
            .build();
    }

    private DeliveryOrderDto updateStatus(UUID deliveryOrderId,
        DeliveryOrderStatus newStatus,
        DeliveryOrderDeliveredCommand command) {
        DeliveryOrder deliveryOrder = deliveryOrderRepository.findById(deliveryOrderId);
        validateOrderNotCanceled(deliveryOrder);
        if (deliveryOrder.getDeliveryTaskId() == null) {
            throw new DeliveryBusinessException(ErrorCodeEnums.DELIVERY_TASK_NOT_FOUND.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_NOT_FOUND.getMessage());
        }
        deliveryTaskService.inProgress(deliveryOrder.getDeliveryTaskId());
        deliveryOrder.updateStatus(newStatus, command);
        DeliveryOrderDto deliveryOrderDto = mapper.domainToDto(deliveryOrderRepository.save(deliveryOrder));

        saveBusinessEvent(deliveryOrderDto);
        return deliveryOrderDto;
    }

    private DeliveryOrderDto updateStatus(UUID deliveryOrderId, DeliveryOrderStatus newStatus) {
        return updateStatus(deliveryOrderId, newStatus, null);
    }

    @RetryableTransaction
    public DeliveryOrderDto update(UUID deliveryOrderId, UpdateDeliveryOrderCommand command) {
        DeliveryOrder deliveryOrder = deliveryOrderRepository.findById(deliveryOrderId);
        validateOrderNotCanceled(deliveryOrder);
        saveUpdatedBusinessEvent(deliveryOrderId, command, deliveryOrder);
        deliveryOrder.update(command);
        return mapper.domainToDto(deliveryOrderRepository.save(deliveryOrder));
    }

    private void saveUpdatedBusinessEvent(UUID deliveryOrderId,
        UpdateDeliveryOrderCommand command,
        DeliveryOrder previousDeliveryOrder) {
        if (Objects.equals(command.getRescheduleType(), previousDeliveryOrder.getRescheduleType())) {
            return;
        }
        DeliveryOrderUpdatedEventDto eventDto = DeliveryOrderUpdatedEventDto.builder()
            .deliveryOrderId(deliveryOrderId)
            .source(DeliveryOrderUpdatedEventDto.UpdateDeliveryOrder.builder()
                .rescheduleType(previousDeliveryOrder.getRescheduleType())
                .build())
            .target(DeliveryOrderUpdatedEventDto.UpdateDeliveryOrder.builder()
                .rescheduleType(command.getRescheduleType())
                .build())
            .build();
        businessEventDispatcher.dispatch(BusinessEventFactory.build(DeliveryOrderUpdatedPayloadDto.builder()
            .deliveryOrderId(deliveryOrderId)
            .data(eventDto)
            .build()));
    }

    @RetryableTransaction
    public DeliveryOrderDto unload(UUID deliveryOrderId, DeliveryOrderUnloadCommand command) {
        DeliveryOrder deliveryOrder = deliveryOrderRepository.findById(deliveryOrderId);
        validateOrderNotCanceled(deliveryOrder);
        validateOrderCanBeUnload(deliveryOrder);

        deliveryOrder.updateDeliveredItems(command.getUpdateDeliveryOrderItemDtos());
        deliveryOrder.updateStatus(DeliveryOrderStatus.UNLOADED, null);
        DeliveryOrderDto deliveryOrderDto = mapper.domainToDto(deliveryOrderRepository.save(deliveryOrder));

        businessEventDispatcher.dispatch(BusinessEventFactory.build(DeliveryOrderUnloadedPayloadDto.builder()
            .deliveryOrderId(deliveryOrderDto.getId())
            .data(deliveryOrderDto)
            .build()));
        return deliveryOrderDto;
    }

    private void validateOrderCanBeUnload(DeliveryOrder deliveryOrder) {
        if (!DeliveryOrderStatus.ARRIVED.equals(deliveryOrder.getStatus())
            && !DeliveryOrderStatus.UNLOADED.equals(deliveryOrder.getStatus())) {
            throw new WmsBusinessException(ErrorCodeEnums.DELIVERY_ORDER_NOT_ARRIVED.getCode(),
                ErrorCodeEnums.DELIVERY_ORDER_NOT_ARRIVED.getMessage());
        }
    }

    private void validateOrderNotCanceled(DeliveryOrder deliveryOrder) {
        if (DeliveryOrderStatus.CANCELED.equals(deliveryOrder.getStatus())) {
            throw new WmsBusinessException(ErrorCodeEnums.DELIVERY_ORDER_CANCELED.getCode(),
                ErrorCodeEnums.DELIVERY_ORDER_CANCELED.getMessage());
        }
    }

    public void saveBusinessEvent(DeliveryOrderDto deliveryOrderDto) {
        if (DeliveryOrderStatus.IN_TRANSIT.equals(deliveryOrderDto.getStatus())) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(DeliveryOrderInTransitPayloadDto.builder()
                .deliveryOrderId(deliveryOrderDto.getId())
                .data(deliveryOrderDto)
                .build()));
        } else if (DeliveryOrderStatus.ARRIVED.equals(deliveryOrderDto.getStatus())) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(DeliveryOrderArrivedPayloadDto.builder()
                .deliveryOrderId(deliveryOrderDto.getId())
                .data(deliveryOrderDto)
                .build()));
        } else if (DeliveryOrderStatus.DELIVERED.equals(deliveryOrderDto.getStatus())) {
            DispatchResponseDto responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
                DeliveryOrderDeliveredPayloadDto.builder()
                    .deliveryOrderId(deliveryOrderDto.getId())
                    .data(deliveryOrderDto)
                    .build()));
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
        }
    }

    @Transactional
    public void createOrUpdate(ShopifyOrderForDeliveryDto shopifyOrderDto) {
        boolean mfcOrder = shopifyOrderDto.getTags().contains(MERCASO_SELLER);
        if (!mfcOrder) {
            log.info("Ignoring non-MFC order: {}", shopifyOrderDto.getName());
            return;
        }
        String deliveryDate = DeliveryOrder.builder().build().convertDeliveryDate(shopifyOrderDto.getTags());
        if (deliveryDate != null && LocalDate.parse(deliveryDate).isBefore(LocalDate.now().minusMonths(1))) {
            return;
        }

        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock((shopifyOrderDto.getName() + shopifyOrderDto.getId()).hashCode(),
            "DeliveryOrderApplicationService.createOrUpdate");

        DeliveryOrder deliveryOrder = deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        processShippingAddressCoordinates(shopifyOrderDto, deliveryOrder);

        if (null == deliveryOrder) {
            deliveryOrder = DeliveryOrder.builder().build().create(shopifyOrderDto);
            setWarehouse(deliveryOrder);
        } else {
            deliveryOrder.update(shopifyOrderDto);
        }
        deliveryOrder.setCustomer(customerService.findOrSaveCustomer(shopifyOrderDto.getCustomer()));
        setFieldsFromIms(deliveryOrder);
        deliveryOrderRepository.save(deliveryOrder);
    }

    private void processShippingAddressCoordinates(ShopifyOrderForDeliveryDto shopifyOrderDto, DeliveryOrder existingOrder) {
        ShippingAddressDto shippingAddress = shopifyOrderDto.getShippingAddress();

        if (!hasValidCoordinates(shippingAddress)) {
            if (existingOrder != null) {
                Address existingAddress = existingOrder.getAddress();

                if (isSameAddress(existingAddress, shippingAddress) && hasValidCoordinates(existingAddress)) {
                    copyCoordinates(existingAddress, shippingAddress);
                    return;
                }
            }

            log.info("Processing order with missing coordinates: {}. Attempting to geocode address.",
                shopifyOrderDto.getName());
            updateShippingAddressCoordinates(shopifyOrderDto);
        }
    }

    private void copyCoordinates(Address source, ShippingAddressDto target) {
        target.setLatitude(source.getLatitude());
        target.setLongitude(source.getLongitude());
    }

    private boolean isSameAddress(Address address, ShippingAddressDto shippingAddress) {
        return Optional.ofNullable(address)
            .flatMap(addr -> Optional.ofNullable(shippingAddress)
                .filter(ship -> Objects.equals(addr.getCity(), ship.getCity())
                    && Objects.equals(addr.getState(), ship.getProvince())
                    && Objects.equals(addr.getCountry(), ship.getCountry())
                    && Objects.equals(addr.getPostalCode(), ship.getZip())
                    && Objects.equals(addr.getAddressOne(), ship.getAddress1())
                    && Objects.equals(addr.getAddressTwo(), ship.getAddress2())))
            .isPresent();
    }

    private boolean hasValidCoordinates(Address address) {
        return address != null && address.getLatitude() != null && address.getLongitude() != null;
    }

    private boolean hasValidCoordinates(ShippingAddressDto shippingAddress) {
        return shippingAddress != null && shippingAddress.getLatitude() != null && shippingAddress.getLongitude() != null;
    }

    private void updateShippingAddressCoordinates(ShopifyOrderForDeliveryDto shopifyOrderDto) {
        ShippingAddressDto shippingAddress = shopifyOrderDto.getShippingAddress();
        if (shippingAddress == null) {
            return;
        }

        String fullAddress = buildFullAddress(shippingAddress);
        if (!StringUtils.hasText(fullAddress)) {
            log.warn("Cannot geocode address: Incomplete address information for order {}", shopifyOrderDto.getName());
            return;
        }

        try {
            LatLng coordinates = googleMapAdaptor.getCoordinatesByAddress(fullAddress);
            if (coordinates != null) {
                shippingAddress.setLatitude(BigDecimal.valueOf(coordinates.lat));
                shippingAddress.setLongitude(BigDecimal.valueOf(coordinates.lng));
                log.info("Successfully geocoded address for order {}: lat={}, lng={}",
                    shopifyOrderDto.getName(), coordinates.lat, coordinates.lng);
            } else {
                log.warn("Failed to geocode address for order {}: No coordinates returned", shopifyOrderDto.getName());
            }
        } catch (Exception e) {
            log.error("Error geocoding address for order {}: {}", shopifyOrderDto.getName(), e.getMessage(), e);
        }
    }

    private String buildFullAddress(ShippingAddressDto address) {
        StringBuilder sb = new StringBuilder();

        appendIfPresent(sb, address.getAddress1());
        appendIfPresent(sb, address.getAddress2());
        appendIfPresent(sb, address.getCity());
        appendIfPresent(sb, address.getProvince());
        appendIfPresent(sb, address.getZip(), " ");
        appendIfPresent(sb, address.getCountry());

        return sb.toString();
    }

    private void appendIfPresent(StringBuilder sb, String value) {
        appendIfPresent(sb, value, ", ");
    }

    private void appendIfPresent(StringBuilder sb, String value, String separator) {
        if (StringUtils.hasText(value)) {
            if (!sb.isEmpty()) {
                sb.append(separator);
            }
            sb.append(value);
        }
    }

    private void setWarehouse(DeliveryOrder deliveryOrder) {
        deliveryOrder.setWarehouseId(warehouseRepository.findByName("MDC").getId());
    }

    private void setFieldsFromIms(DeliveryOrder deliveryOrder) {
        Set<String> skus = deliveryOrder.getDeliveryOrderItems()
            .stream()
            .map(DeliveryOrderItem::getSkuNumber)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skus)) {
            log.warn("[createOrUpdate] No skus found for shopify order: {}", deliveryOrder.getOrderNumber());
            return;
        }
        List<ItemCategoryDto> itemsBySkus = imsAdaptor.getItemsBySkus(skus.stream().toList());
        deliveryOrder.getDeliveryOrderItems()
            .forEach(deliveryOrderItem -> itemsBySkus.stream()
                .filter(itemCategory -> Objects.equals(itemCategory.getSkuNumber(), deliveryOrderItem.getSkuNumber()))
                .findFirst()
                .ifPresent(itemCategoryDto -> {
                    deliveryOrderItem.setItemId(itemCategoryDto.getId());
                    deliveryOrderItem.setTitle(itemCategoryDto.getTitle());
                    deliveryOrderItem.setPackageSize(itemCategoryDto.getPack());
                    deliveryOrderItem.setCrvPrice(itemCategoryDto.getCrv());
                    deliveryOrderItem.setContainsNicotine(
                        itemCategoryDto.getDepartment() != null && itemCategoryDto.getDepartment()
                            .equalsIgnoreCase("Tobacco"));
                }));
    }

    public void sendInvoice(UUID deliveryOrderId) {
        DeliveryOrder deliveryOrder = deliveryOrderRepository.findById(deliveryOrderId);
        if (deliveryOrder == null) {
            throw new WmsBusinessException(ErrorCodeEnums.DELIVERY_ORDER_NOT_FOUND.getCode(),
                ErrorCodeEnums.DELIVERY_ORDER_NOT_FOUND.getMessage());
        }
        List<DocumentDto> invoiceDocuments = documentQueryService.findDocumentsBy(deliveryOrderId,
            EntityEnums.DELIVERY_ORDER,
            List.of(DocumentType.INVOICE));
        if (CollectionUtils.isEmpty(invoiceDocuments)) {
            log.warn("No invoice documents found for delivery order {}", deliveryOrderId);
            throw new DeliveryBusinessException("No invoice documents found for delivery order");
        }
        Customer customer = deliveryOrder.getCustomer();
        if (customer == null || customer.getEmail() == null) {
            throw new DeliveryBusinessException("Customer email not found for delivery order");
        }
        try {
            onesignalAdaptor.sendEmail(
                customer.getEmail(),
                Map.of("orderNumber", deliveryOrder.getOrderNumber(),
                    "customerName", customer.getFirstName().concat(" ").concat(customer.getLastName()),
                    "invoiceUrl",
                    documentApplicationService.generateInvoiceSignatureWithExpiration(invoiceDocuments.getLast().getFileName()),
                    "deliveryDate",
                    deliveryOrder.getDeliveryDate(),
                    "totalAmount",
                    deliveryOrder.getTotalPrice() != null ? deliveryOrder.getTotalPrice().toString() : "0.0"));
        } catch (Exception e) {
            log.error("Error sending invoice email for delivery order {}: {}", deliveryOrderId, e.getMessage(), e);
            throw new DeliveryBusinessException("Error sending invoice email for delivery order");
        }
    }
}
