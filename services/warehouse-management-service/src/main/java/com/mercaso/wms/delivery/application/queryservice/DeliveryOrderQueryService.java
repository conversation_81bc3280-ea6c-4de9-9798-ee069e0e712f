package com.mercaso.wms.delivery.application.queryservice;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.mapper.deliveryorder.DeliveryOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryOrderQueryService {

    private final DeliveryOrderRepository deliveryOrderRepository;

    private final DeliveryOrderDtoApplicationMapper deliveryOrderDtoApplicationMapper;

    @Value("${shopify.admin-order-url}")
    private String shopifyOrderUrl;

    public DeliveryOrderDto findById(UUID id) {
        return deliveryOrderDtoApplicationMapper.domainToDto(deliveryOrderRepository.findById(id), shopifyOrderUrl);
    }

    public DeliveryOrderDto findByOrderNumber(String orderNumber) {
        return deliveryOrderDtoApplicationMapper.domainToDto(deliveryOrderRepository.findByOrderNumber(orderNumber),
            shopifyOrderUrl);
    }

}
